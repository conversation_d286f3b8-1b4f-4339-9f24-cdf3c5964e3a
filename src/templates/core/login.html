{% extends "base.html" %}

{% block title %}Login - Oficina EPG{% endblock %}

{% block extra_head %}
<style>
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --dark-overlay: rgba(0, 0, 0, 0.6);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
}

body {
    overflow-x: hidden;
    min-height: 100vh;
    background: var(--primary-gradient);
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -1;
}

.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

.login-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    box-shadow: 
        0 20px 40px rgba(31, 38, 135, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accent-gradient);
}

.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.login-title {
    font-family: 'Space Grotesk', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    font-weight: 400;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-control {
    width: 100%;
    padding: 1rem 1.25rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-control:focus {
    outline: none;
    border-color: rgba(79, 172, 254, 0.6);
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.2);
    background: rgba(255, 255, 255, 0.15);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.btn-login {
    width: 100%;
    padding: 1rem 2rem;
    background: var(--accent-gradient);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
    position: relative;
    overflow: hidden;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(79, 172, 254, 0.4);
}

.btn-login:active {
    transform: translateY(0);
}

.login-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.login-footer a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.login-footer a:hover {
    color: white;
}

.alert {
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    border: 1px solid transparent;
}

.alert-success {
    background: rgba(17, 153, 142, 0.2);
    border-color: rgba(17, 153, 142, 0.3);
    color: #38ef7d;
}

.alert-error {
    background: rgba(252, 70, 107, 0.2);
    border-color: rgba(252, 70, 107, 0.3);
    color: #fc466b;
}

.errorlist {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0 0 0;
}

.errorlist li {
    color: #fc466b;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

@media (max-width: 768px) {
    .login-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }
    
    .login-title {
        font-size: 2rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h1 class="login-title">
                <i class="fas fa-user-shield"></i>
                Login
            </h1>
            <p class="login-subtitle">Aceda ao painel administrativo</p>
        </div>

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <form method="post" novalidate>
            {% csrf_token %}
            
            <div class="form-group">
                <label for="{{ form.username.id_for_label }}" class="form-label">
                    <i class="fas fa-user"></i>
                    Utilizador
                </label>
                <input type="text"
                       name="{{ form.username.name }}"
                       id="{{ form.username.id_for_label }}"
                       class="form-control"
                       placeholder="Digite o seu utilizador"
                       value="{{ form.username.value|default:'' }}"
                       required>
                {% if form.username.errors %}
                    <ul class="errorlist">
                        {% for error in form.username.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.password.id_for_label }}" class="form-label">
                    <i class="fas fa-lock"></i>
                    Palavra-passe
                </label>
                <input type="password"
                       name="{{ form.password.name }}"
                       id="{{ form.password.id_for_label }}"
                       class="form-control"
                       placeholder="Digite a sua palavra-passe"
                       required>
                {% if form.password.errors %}
                    <ul class="errorlist">
                        {% for error in form.password.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
                <div class="alert alert-error">
                    {% for error in form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}

            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt"></i>
                Entrar
            </button>
        </form>

        <div class="login-footer">
            <a href="{% url 'landing' %}">
                <i class="fas fa-arrow-left"></i>
                Voltar ao início
            </a>
        </div>
    </div>
</div>
{% endblock %}
