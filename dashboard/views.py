# dashboard/views.py
from django.shortcuts import render, redirect
from django.core.paginator import Paginator
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from .models import User, Servico, Agendamento
from django.contrib import messages
from .forms import ServicoForm


@login_required
@staff_member_required
def dashboard_home(request):
    """Dashboard home view - requires staff access"""
    # Paginated users
    users = User.objects.all().order_by('id')
    paginator = Paginator(users, 10)  # 10 users per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Additional dashboard data
    total_users = User.objects.count()
    total_servicos = Servico.objects.count()
    recent_agendamentos = Agendamento.objects.order_by('-data_hora')[:5]

    context = {
        'page_obj': page_obj,
        'total_users': total_users,
        'total_servicos': total_servicos,
        'recent_agendamentos': recent_agendamentos,
    }
    return render(request, 'dashboard/dashboard.html', context)


@login_required
@staff_member_required
def dashboard_precos(request):
    """Dashboard prices view"""
    return render(request, 'dashboard/precos.html')


@login_required
@staff_member_required
def dashboard_servicos(request):
    """Dashboard services view"""
    return render(request, 'dashboard/servicos.html')


def servicos_list(request):
    """Public services list view"""
    servicos = Servico.objects.all()
    return render(request, 'servicos_list.html', {'servicos': servicos})


@login_required
@staff_member_required
def add_servico(request):
    """Add new service view - requires staff access"""
    if request.method == 'POST':
        form = ServicoForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Serviço adicionado com sucesso!')
            return redirect('dashboard_home')
    else:
        form = ServicoForm()
    return render(request, 'dashboard/add_servico.html', {'form': form})