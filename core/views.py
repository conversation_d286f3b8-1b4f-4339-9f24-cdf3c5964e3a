from django.shortcuts import render, redirect
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth import login as auth_login, logout as auth_logout
from django.contrib import messages
from django.core.paginator import Paginator
from dashboard.models import Servico


def landing_page(request):
    return render(request, 'core/landing.html')


def servicos_list(request):
    """Public page displaying all available services"""
    servicos = Servico.objects.all().order_by('nome')

    # Add pagination
    paginator = Paginator(servicos, 6)  # Show 6 services per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_servicos': servicos.count(),
    }
    return render(request, 'core/servicos_list.html', context)


def login_view(request):
    """Login view with custom template"""
    if request.user.is_authenticated:
        return redirect('dashboard_home')

    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            auth_login(request, user)
            messages.success(request, f'Bem-vindo, {user.username}!')

            # Redirect to dashboard if staff, otherwise to services
            if user.is_staff:
                return redirect('dashboard_home')
            else:
                return redirect('servicos_list')
        else:
            messages.error(request, 'Credenciais inválidas. Tente novamente.')
    else:
        form = AuthenticationForm()

    return render(request, 'core/login.html', {'form': form})


def logout_view(request):
    """Logout view"""
    if request.user.is_authenticated:
        username = request.user.username
        auth_logout(request)
        messages.success(request, f'Até logo, {username}!')

    return redirect('landing')
